import Image from 'next/image'
import { Check } from 'lucide-react'

interface HeaderProps {
  progressPercentage: number; // New prop for progress percentage
}

export function Header({ progressPercentage }: HeaderProps) {
  // Ensure the percentage is between 0 and 100
  const clampedPercentage = Math.min(100, Math.max(0, progressPercentage));
  
  return (
    <header className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 sm:mb-8 space-y-4 sm:space-y-0">
      <Image
        priority
        src="/meet-george-logo.png"
        alt="Meet George Logo"
        width={180}
        height={60}
        style={{ height: 'auto' }}
        className="w-32 sm:w-auto"
      />
      <div className="flex items-center space-x-2 w-full sm:w-auto">
        <div className="w-32 sm:w-48 h-2 bg-gray-200 rounded-full flex">
          <div
            className="h-full bg-green-500 rounded-full"
            style={{ width: `${clampedPercentage}%` }}
          ></div>
        </div>
        <Check className="w-4 h-4 sm:w-5 sm:h-5 text-green-500" />
        <span className="text-xs sm:text-sm text-gray-600 font-medium">Switched</span>
      </div>
    </header>
  )
}
